"""
Forms Builder Module

Handles interactive PDF form creation with support for text fields,
checkboxes, radio buttons, and other AcroForm elements.
"""

from typing import Optional, Dict, Any, Union, List
from pathlib import Path
import io

from reportlab.pdfgen import canvas
from reportlab.lib.pagesizes import letter, A4
from reportlab.lib.units import inch, cm
from reportlab.pdfbase import pdfform
from reportlab.lib.colors import black, white, blue

from .exceptions import ValidationError, FormError, RenderError


class FormBuilder:
    """
    Builder for interactive PDF forms.
    
    Provides high-level interface for creating forms with various
    interactive elements and validation.
    """
    
    def __init__(self, config: Dict[str, Any]):
        """Initialize form builder with configuration."""
        self.config = config
        self._forms = {}
        
    def new_form(self,
                output_path: Optional[Union[str, Path]] = None,
                page_size: tuple = A4) -> 'FormInstance':
        """
        Create a new form instance.
        
        Args:
            output_path: Optional output file path
            page_size: Page size tuple
            
        Returns:
            FormInstance for method chaining
        """
        form_instance = FormInstance(
            output_path=output_path,
            page_size=page_size,
            config=self.config
        )
        
        form_id = id(form_instance)
        self._forms[form_id] = form_instance
        return form_instance


class FormInstance:
    """
    Individual form instance with fluent interface.
    
    Supports method chaining for building interactive forms.
    """
    
    def __init__(self,
                 output_path: Optional[Union[str, Path]],
                 page_size: tuple,
                 config: Dict[str, Any]):
        """Initialize form instance."""
        self.output_path = output_path
        self.page_size = page_size
        self.config = config
        self.fields = []
        self.canvas = None
        
    def add_text_field(self,
                      name: str,
                      x: float,
                      y: float,
                      width: float = 200,
                      height: float = 20,
                      default_value: str = "",
                      required: bool = False) -> 'FormInstance':
        """
        Add a text input field to the form.
        
        Args:
            name: Field name (must be unique)
            x: X coordinate
            y: Y coordinate  
            width: Field width
            height: Field height
            default_value: Default text value
            required: Whether field is required
            
        Returns:
            Self for method chaining
        """
        if not name:
            raise ValidationError("Field name cannot be empty")
            
        field = {
            'type': 'text',
            'name': name,
            'x': x,
            'y': y,
            'width': width,
            'height': height,
            'default_value': default_value,
            'required': required
        }
        
        self.fields.append(field)
        return self
    
    def add_checkbox(self,
                    name: str,
                    x: float,
                    y: float,
                    size: float = 12,
                    checked: bool = False,
                    label: str = "") -> 'FormInstance':
        """
        Add a checkbox to the form.
        
        Args:
            name: Field name (must be unique)
            x: X coordinate
            y: Y coordinate
            size: Checkbox size
            checked: Default checked state
            label: Optional label text
            
        Returns:
            Self for method chaining
        """
        if not name:
            raise ValidationError("Field name cannot be empty")
            
        field = {
            'type': 'checkbox',
            'name': name,
            'x': x,
            'y': y,
            'size': size,
            'checked': checked,
            'label': label
        }
        
        self.fields.append(field)
        return self
    
    def add_radio_group(self,
                       group_name: str,
                       options: List[Dict[str, Any]]) -> 'FormInstance':
        """
        Add a radio button group to the form.
        
        Args:
            group_name: Group name (must be unique)
            options: List of option dicts with keys: name, x, y, label, selected
            
        Returns:
            Self for method chaining
        """
        if not group_name:
            raise ValidationError("Group name cannot be empty")
        if not options:
            raise ValidationError("Radio group must have at least one option")
            
        for option in options:
            if 'name' not in option or 'x' not in option or 'y' not in option:
                raise ValidationError("Radio option must have name, x, and y")
                
            field = {
                'type': 'radio',
                'group_name': group_name,
                'name': option['name'],
                'x': option['x'],
                'y': option['y'],
                'label': option.get('label', ''),
                'selected': option.get('selected', False)
            }
            
            self.fields.append(field)
        
        return self
    
    def add_signature_field(self,
                           name: str,
                           x: float,
                           y: float,
                           width: float = 200,
                           height: float = 50) -> 'FormInstance':
        """
        Add a signature field to the form.
        
        Args:
            name: Field name (must be unique)
            x: X coordinate
            y: Y coordinate
            width: Field width
            height: Field height
            
        Returns:
            Self for method chaining
        """
        if not name:
            raise ValidationError("Field name cannot be empty")
            
        field = {
            'type': 'signature',
            'name': name,
            'x': x,
            'y': y,
            'width': width,
            'height': height
        }
        
        self.fields.append(field)
        return self
    
    def render_to_bytes(self) -> bytes:
        """
        Render form to bytes.
        
        Returns:
            PDF content as bytes
        """
        try:
            buffer = io.BytesIO()
            c = canvas.Canvas(buffer, pagesize=self.page_size)
            
            # Render all form fields
            for field in self.fields:
                self._render_field(c, field)
            
            c.save()
            return buffer.getvalue()
        except Exception as e:
            raise RenderError(f"Failed to render form: {e}") from e
    
    def render_to_file(self, output_path: Union[str, Path]) -> None:
        """
        Render form to file.
        
        Args:
            output_path: Output file path
        """
        try:
            c = canvas.Canvas(str(output_path), pagesize=self.page_size)
            
            # Render all form fields
            for field in self.fields:
                self._render_field(c, field)
            
            c.save()
        except Exception as e:
            raise RenderError(f"Failed to save form to {output_path}: {e}") from e
    
    def _render_field(self, canvas_obj: canvas.Canvas, field: Dict[str, Any]) -> None:
        """
        Render individual form field.
        
        Args:
            canvas_obj: ReportLab canvas object
            field: Field configuration dictionary
        """
        field_type = field['type']
        
        if field_type == 'text':
            canvas_obj.acroForm.textfield(
                name=field['name'],
                x=field['x'],
                y=field['y'],
                width=field['width'],
                height=field['height'],
                value=field['default_value'],
                forceBorder=True
            )
        
        elif field_type == 'checkbox':
            canvas_obj.acroForm.checkbox(
                name=field['name'],
                x=field['x'],
                y=field['y'],
                size=field['size'],
                checked=field['checked'],
                forceBorder=True
            )
            
            # Add label if provided
            if field['label']:
                canvas_obj.drawString(
                    field['x'] + field['size'] + 5,
                    field['y'],
                    field['label']
                )
        
        elif field_type == 'radio':
            canvas_obj.acroForm.radio(
                name=field['group_name'],
                value=field['name'],
                x=field['x'],
                y=field['y'],
                selected=field['selected']
            )
            
            # Add label if provided
            if field['label']:
                canvas_obj.drawString(
                    field['x'] + 15,
                    field['y'],
                    field['label']
                )
        
        elif field_type == 'signature':
            # Create signature field as text field with special formatting
            canvas_obj.acroForm.textfield(
                name=field['name'],
                x=field['x'],
                y=field['y'],
                width=field['width'],
                height=field['height'],
                value="",
                forceBorder=True
            )
            
            # Add signature line
            canvas_obj.line(
                field['x'],
                field['y'],
                field['x'] + field['width'],
                field['y']
            )
