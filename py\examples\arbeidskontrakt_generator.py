"""
Arbeidskontrakt Generator

Demonstrates creating a Norwegian employment contract (arbeidskontrakt) 
using the PDF engine with interactive form fields.
"""

import sys
from pathlib import Path

# Add parent directory to path for imports
sys.path.append(str(Path(__file__).parent.parent))

from pdf_engine import PDFEngine
from pdf_engine.layout import LayoutManager, Position, Dimensions, Margins, Units


def create_arbeidskontrakt():
    """Create an interactive Norwegian employment contract."""
    
    # Initialize the PDF engine
    engine = PDFEngine()
    
    # Create a new form for the contract
    form = engine.create_form("arbeidskontrakt_ringerike_landskap.pdf")
    
    # Get layout manager for positioning
    layout = engine.get_layout_manager()
    
    # Create a grid layout for consistent positioning
    grid = layout.create_grid(
        rows=30, cols=4,
        margins=Margins(top=2, bottom=2, left=2, right=2, unit=Units.CM)
    )
    
    # Add form fields for the contract
    
    # Employee name field
    form.add_text_field(
        name="employee_name",
        x=200, y=720,
        width=250, height=20,
        default_value="",
        required=True
    )
    
    # Employee ID number
    form.add_text_field(
        name="employee_id",
        x=200, y=690,
        width=150, height=20,
        default_value="",
        required=True
    )
    
    # Employee address
    form.add_text_field(
        name="employee_address",
        x=200, y=660,
        width=300, height=20,
        default_value="",
        required=True
    )
    
    # Start date
    form.add_text_field(
        name="start_date",
        x=200, y=600,
        width=120, height=20,
        default_value="",
        required=True
    )
    
    # Employment type - radio buttons
    employment_options = [
        {"name": "permanent", "x": 200, "y": 570, "label": "Fast ansettelse", "selected": True},
        {"name": "temporary", "x": 200, "y": 545, "label": "Midlertidig ansettelse", "selected": False}
    ]
    form.add_radio_group("employment_type", employment_options)
    
    # Probation period checkbox
    form.add_checkbox(
        name="probation_period",
        x=200, y=510,
        size=12,
        checked=False,
        label="Prøvetid (maks 6 måneder)"
    )
    
    # Probation months field
    form.add_text_field(
        name="probation_months",
        x=400, y=510,
        width=50, height=20,
        default_value="",
        required=False
    )
    
    # Monthly salary
    form.add_text_field(
        name="monthly_salary",
        x=200, y=470,
        width=120, height=20,
        default_value="",
        required=True
    )
    
    # Bank account number
    form.add_text_field(
        name="bank_account",
        x=200, y=440,
        width=200, height=20,
        default_value="",
        required=True
    )
    
    # Vacation weeks (default 5)
    form.add_text_field(
        name="vacation_weeks",
        x=200, y=380,
        width=50, height=20,
        default_value="5",
        required=True
    )
    
    # Car allowance checkbox (default checked with 300 kr)
    form.add_checkbox(
        name="car_allowance",
        x=200, y=350,
        size=12,
        checked=True,
        label="Kilometergodtgjørelse kr 300,- per måned"
    )
    
    # Tariff agreement checkbox
    form.add_checkbox(
        name="tariff_agreement",
        x=200, y=320,
        size=12,
        checked=False,
        label="Omfattet av tariffavtale"
    )
    
    # Tariff agreement name field
    form.add_text_field(
        name="tariff_name",
        x=400, y=320,
        width=200, height=20,
        default_value="",
        required=False
    )
    
    # Signature fields
    form.add_signature_field(
        name="employer_signature",
        x=100, y=150,
        width=200, height=40
    )
    
    form.add_signature_field(
        name="employee_signature",
        x=350, y=150,
        width=200, height=40
    )
    
    # Date field for signing
    form.add_text_field(
        name="signing_date",
        x=275, y=100,
        width=120, height=20,
        default_value="",
        required=True
    )
    
    # Render to file
    form.render_to_file("arbeidskontrakt_ringerike_landskap.pdf")
    print("Arbeidskontrakt created: arbeidskontrakt_ringerike_landskap.pdf")
    
    # Also create a static version with the contract text
    create_static_contract_template()


def create_static_contract_template():
    """Create a static contract template with the full text."""
    
    engine = PDFEngine()
    doc = engine.create_document("arbeidskontrakt_template.pdf")
    
    # Add the contract content
    doc.add_title("ARBEIDSAVTALE") \
       .add_spacer(10) \
       .add_paragraph("Arbeidsgiver: Ringerike Landskap AS") \
       .add_paragraph("Organisasjonsnummer: 924 826 541") \
       .add_paragraph("Adresse: c/o Kim Tuvsjøen, Birchs vei 7, 3530 Røyse") \
       .add_spacer(15) \
       .add_paragraph("Arbeidstaker: ________________________________") \
       .add_paragraph("Fødselsnummer: ________________________________") \
       .add_paragraph("Adresse: ________________________________") \
       .add_spacer(20) \
       .add_title("1. ARBEIDSSTED", style="Heading2") \
       .add_paragraph("Arbeidstakeren skal arbeide ved Ringerike Landskap AS med hovedarbeidssted i Røyse kommune. Arbeidstakeren kan også utføre arbeid på ulike prosjektsteder i Ringerike og omkringliggende kommuner, i henhold til virksomhetens behov som anleggsgartnerforetak.") \
       .add_spacer(15) \
       .add_title("2. STILLINGSBESKRIVELSE", style="Heading2") \
       .add_paragraph("Arbeidstakeren ansettes som anleggsgartnerfagarbeider og skal utføre arbeidsoppgaver innenfor anleggsgartnerfaget, inkludert men ikke begrenset til:") \
       .add_paragraph("• Anleggsarbeid og hagearbeid") \
       .add_paragraph("• Graving og terrengarbeid") \
       .add_paragraph("• Planting og vedlikehold av grøntanlegg") \
       .add_paragraph("• Bruk av maskiner og verktøy") \
       .add_paragraph("• Andre oppgaver som naturlig hører til stillingen") \
       .add_page_break()
    
    # Continue with more sections
    doc.add_title("3. ANSETTELSESFORHOLD", style="Heading2") \
       .add_paragraph("Oppstart: ________________________________") \
       .add_paragraph("Ansettelsestype: ☐ Fast ansettelse ☐ Midlertidig ansettelse") \
       .add_paragraph("Prøvetid: ☐ Ingen prøvetid ☐ _____ måneder (maks 6 måneder)") \
       .add_spacer(15) \
       .add_title("4. ARBEIDSTID", style="Heading2") \
       .add_paragraph("Ordinær arbeidstid: 37,5 timer per uke") \
       .add_paragraph("Arbeidstidsordning: Dagarbeid, mandag til fredag") \
       .add_paragraph("Arbeidstid: 07:00 - 15:30 (inkludert 30 min lunsj)") \
       .add_spacer(15) \
       .add_title("5. LØNN OG GODTGJØRELSER", style="Heading2") \
       .add_paragraph("Grunnlønn: Kr ________________________________ per måned") \
       .add_paragraph("Lønnsutbetaling: Den 5. i hver måned") \
       .add_paragraph("Kilometergodtgjørelse ved bruk av privat bil: Kr 300,- per måned") \
       .add_spacer(15) \
       .add_title("6. FERIE", style="Heading2") \
       .add_paragraph("Arbeidstakeren har rett til 5 ukers ferie per år i henhold til ferieloven.") \
       .add_paragraph("Feriepenger utbetales med 12% av opptjent lønn") \
       .add_spacer(20) \
       .add_paragraph("Dato: ________________________________") \
       .add_spacer(30) \
       .add_paragraph("Arbeidsgiver: Ringerike Landskap AS") \
       .add_spacer(20) \
       .add_paragraph("________________________________") \
       .add_paragraph("Underskrift arbeidsgiver") \
       .add_spacer(30) \
       .add_paragraph("________________________________") \
       .add_paragraph("Underskrift arbeidstaker")
    
    doc.render_to_file("arbeidskontrakt_template.pdf")
    print("Static contract template created: arbeidskontrakt_template.pdf")


if __name__ == "__main__":
    create_arbeidskontrakt()
