"""
PDF Engine - Modular PDF Generation System

A unified, enterprise-grade PDF generation framework with clean abstraction layers,
minimal dependencies, and extensible architecture for static and interactive documents.

Core Principles:
- Single responsibility modules
- Clean abstraction boundaries  
- Plugin-style extensibility
- Minimal external dependencies
- Future-proof architecture
"""

from .core import PDFEngine
from .document import DocumentBuilder
from .forms import FormBuilder
from .layout import LayoutManager
from .exceptions import PDFEngineError, ValidationError

__version__ = "1.0.0"
__all__ = [
    "PDFEngine",
    "DocumentBuilder", 
    "FormBuilder",
    "LayoutManager",
    "PDFEngineError",
    "ValidationError"
]
