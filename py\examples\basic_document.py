"""
Basic Document Example

Demonstrates creating a simple PDF document with text, tables, and basic formatting.
"""

import sys
from pathlib import Path

# Add parent directory to path for imports
sys.path.append(str(Path(__file__).parent.parent))

from pdf_engine import PDFEngine


def create_basic_document():
    """Create a basic PDF document with various elements."""
    
    # Initialize the PDF engine
    engine = PDFEngine()
    
    # Create a new document
    doc = engine.create_document("basic_document.pdf")
    
    # Add content using method chaining
    doc.add_title("Sample Document") \
       .add_paragraph("This is a sample document created with the PDF Engine.") \
       .add_paragraph("The engine provides a clean, modular interface for PDF generation.") \
       .add_spacer(20) \
       .add_title("Features", style="Heading1") \
       .add_paragraph("• Modular architecture") \
       .add_paragraph("• Clean abstraction layers") \
       .add_paragraph("• Extensible design") \
       .add_paragraph("• Minimal dependencies") \
       .add_spacer(20)
    
    # Add a table
    table_data = [
        ["Feature", "Status", "Priority"],
        ["Document Generation", "Complete", "High"],
        ["Form Creation", "Complete", "High"],
        ["Layout Management", "Complete", "Medium"],
        ["Advanced Graphics", "Planned", "Low"]
    ]
    
    doc.add_title("Project Status", style="Heading1") \
       .add_table(table_data, headers=None) \
       .add_spacer(20) \
       .add_paragraph("This document demonstrates the basic capabilities of the PDF engine.")
    
    # Render to file
    doc.render_to_file("basic_document.pdf")
    print("Basic document created: basic_document.pdf")
    
    # Also demonstrate rendering to bytes
    pdf_bytes = doc.render_to_bytes()
    print(f"Document size: {len(pdf_bytes)} bytes")


if __name__ == "__main__":
    create_basic_document()
