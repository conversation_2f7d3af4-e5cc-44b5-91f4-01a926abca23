"""
PDF Engine Core

Central abstraction layer that coordinates all PDF generation operations.
Provides a unified interface while maintaining clean separation of concerns.
"""

from typing import Optional, Dict, Any, Union
from pathlib import Path
import io

from .exceptions import PDFEngineError, ConfigurationError
from .document import DocumentBuilder
from .forms import FormBuilder
from .layout import LayoutManager


class PDFEngine:
    """
    Core PDF generation engine with modular architecture.
    
    Provides unified interface for creating static and interactive PDFs
    while maintaining clean abstraction boundaries and extensibility.
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        Initialize PDF engine with optional configuration.
        
        Args:
            config: Optional configuration dictionary
        """
        self.config = config or {}
        self._validate_config()
        
        # Initialize core modules
        self.document = DocumentBuilder(self.config)
        self.forms = FormBuilder(self.config)
        self.layout = LayoutManager(self.config)
        
    def _validate_config(self) -> None:
        """Validate engine configuration."""
        if not isinstance(self.config, dict):
            raise ConfigurationError("Configuration must be a dictionary")
    
    def create_document(self, 
                       output_path: Optional[Union[str, Path]] = None,
                       **kwargs) -> 'DocumentBuilder':
        """
        Create a new document builder instance.
        
        Args:
            output_path: Optional output file path
            **kwargs: Additional document options
            
        Returns:
            DocumentBuilder instance
        """
        return self.document.new_document(output_path, **kwargs)
    
    def create_form(self,
                   output_path: Optional[Union[str, Path]] = None,
                   **kwargs) -> 'FormBuilder':
        """
        Create a new interactive form builder instance.
        
        Args:
            output_path: Optional output file path
            **kwargs: Additional form options
            
        Returns:
            FormBuilder instance
        """
        return self.forms.new_form(output_path, **kwargs)
    
    def get_layout_manager(self) -> 'LayoutManager':
        """
        Get the layout manager for advanced layout operations.
        
        Returns:
            LayoutManager instance
        """
        return self.layout
    
    def render_to_bytes(self, builder) -> bytes:
        """
        Render a document/form builder to bytes.
        
        Args:
            builder: DocumentBuilder or FormBuilder instance
            
        Returns:
            PDF content as bytes
        """
        try:
            return builder.render_to_bytes()
        except Exception as e:
            raise PDFEngineError(f"Failed to render PDF: {e}") from e
    
    def render_to_file(self, builder, output_path: Union[str, Path]) -> None:
        """
        Render a document/form builder to file.
        
        Args:
            builder: DocumentBuilder or FormBuilder instance
            output_path: Output file path
        """
        try:
            builder.render_to_file(output_path)
        except Exception as e:
            raise PDFEngineError(f"Failed to save PDF to {output_path}: {e}") from e
