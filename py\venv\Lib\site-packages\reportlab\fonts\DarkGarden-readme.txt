Dark Garden
===========



GENERAL

Dark Garden is a decorative outline font of unusual shape. The typeface
is based on author's original hand drawings. The letterform is complex,
with all characters decorated with spikes resembling thorns or flames,
character spacing is very dense. Such a theme makes it a great font for
titles, banners, logos etc.

As of version 1.1, Dark Garden includes letters of the English alphabet,
Polish and German diacritic characters and some punctuation. Unicode
encoding is used. The font is equipped with hinting information to allow
high quality rendering at small point sizes. Only horizontal and
vertical hints are currently used, future versions might take advantage
of truetype hinting instructions some day. There are also a lot of
kerning pairs defined in the font - they were chosen after looking at a
choice of Polish and English texts and selecting the character pairs
that needed manual kerning.

The font was created with PfaEdit / FontForge
(http://fontforge.sourceforge.net/) - an opensource font editing tool.
Pfaedit uses its own text-based file format which can then be exported
to truetype, postscript, opentype and many other font formats. This
makes it possible to generate font files for Dark Garden in any of the
many supported formats, so it can be used on almost any platform. So
far, the truetype version has been tested on X11 (Linux) and Windows,
the opentype version worked on Windows but not X11. Truetype should also
work on Mac OS.



INSTALLATION

Installation is described for the truetype version of the font (which is
what most users will need).

On Windows:
* Extract .ttf files from the archive into a temporary directory
* In the Control Panel choose Fonts
* From the File menu choose Install New Font
* Browse to the temporary directory you chose
* "Dark Garden" should appear in the font list - click OK (this may
  require Administrator privileges)

On Linux / UNIX (using KDE):
* Extract .ttf files from the archive into a temporary directory
* In the Control Center choose Font Installer in the System
  Administration tree
* Click on "Administrator Mode" and enter root's password
* Click on "Add Fonts" icon
* Select the .ttf file extracted from the archive and click OK

On Linux / UNIX (using commandline)
  unzip darkgarden-*.ttf.zip
  su
  ginstall -o root -m 644 DarkGardenMK.ttf /usr/X11R6/lib/fonts/TTF
  fc-cache -f



COPYRIGHT AND LICENSE

Copyright (C) 1999, 2000, 2004 Michal Kosmulski 
<<EMAIL>>

This font is free software; you can redistribute it and/or modify
it under the terms of the GNU General Public License as published by
the Free Software Foundation; either version 2 of the License, or
(at your option) any later version.

This font is distributed in the hope that it will be useful,
but WITHOUT ANY WARRANTY; without even the implied warranty of
MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
GNU General Public License for more details.

You should have received a copy of the GNU General Public License
along with this font; if not, write to the Free Software
Foundation, Inc., 675 Mass Ave, Cambridge, MA 02139, USA.

As a special exception, if you create a document which uses
this font, and embed this font or unaltered portions of this font into
the document, this font does not by itself cause the resulting
document to be covered by the GNU General Public License.  This
exception does not however invalidate any other reasons why the
document might be covered by the GNU General Public License.  If you
modify this font, you may extend this exception to your version of the
font, but you are not obligated to do so. If you do not wish to do so,
delete this exception statement from your version.

The text of this license can be found in file COPYING.txt in the
distribution archives. The GNU General Public License text can be found
in file COPYING-GPL.txt.



CONTACTING THE AUTHOR

Send e-mail with questions, bug reports and patches to Michal Kosmulski,
<EMAIL>

The homepage of Dark Garden Font is located at
http://darkgarden.sourceforge.net/.
